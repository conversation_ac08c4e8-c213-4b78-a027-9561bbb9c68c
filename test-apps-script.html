<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Google Apps Script</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h3>🧪 Test Google Apps Script</h3>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <strong>Testing URL:</strong><br>
                            https://script.google.com/macros/s/AKfycbzFMUviqAfamV8M11FW_jeMfeLF2j-Z1PUy1O9vGPDaEEWImuPQiu7sNeWa6WTk0Hpk/exec
                        </div>
                        
                        <button id="testGetBtn" class="btn btn-primary me-2">Test GET Request</button>
                        <button id="testPostBtn" class="btn btn-success me-2">Test POST Request</button>
                        <button id="testFormBtn" class="btn btn-info">Test Form Submission</button>
                        
                        <div id="results" class="mt-4"></div>
                        
                        <div class="mt-4">
                            <h5>Quick Test Form:</h5>
                            <form id="quickTestForm">
                                <div class="row">
                                    <div class="col-md-6 mb-2">
                                        <input type="text" class="form-control" id="testName" placeholder="Test Name" value="Test User">
                                    </div>
                                    <div class="col-md-6 mb-2">
                                        <input type="email" class="form-control" id="testEmail" placeholder="Test Email" value="<EMAIL>">
                                    </div>
                                </div>
                                <div class="mb-2">
                                    <textarea class="form-control" id="testMessage" placeholder="Test Message" rows="2">This is a test message from the debug tool.</textarea>
                                </div>
                                <button type="submit" class="btn btn-primary">Submit Test Form</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const SCRIPT_URL = 'https://script.google.com/macros/s/AKfycbzFMUviqAfamV8M11FW_jeMfeLF2j-Z1PUy1O9vGPDaEEWImuPQiu7sNeWa6WTk0Hpk/exec';
        
        document.getElementById('testGetBtn').addEventListener('click', testGet);
        document.getElementById('testPostBtn').addEventListener('click', testPost);
        document.getElementById('testFormBtn').addEventListener('click', testFormSubmission);
        document.getElementById('quickTestForm').addEventListener('submit', handleQuickTest);
        
        async function testGet() {
            addResult('🔍 Testing GET request...', 'info');
            try {
                const response = await fetch(SCRIPT_URL);
                const text = await response.text();
                addResult(`✅ GET Response: ${text}`, 'success');
            } catch (error) {
                addResult(`❌ GET Error: ${error.message}`, 'error');
            }
        }
        
        async function testPost() {
            addResult('📤 Testing POST request...', 'info');
            try {
                const testData = {
                    name: 'Test User',
                    email: '<EMAIL>',
                    message: 'Test message from debug tool',
                    timestamp: new Date().toISOString()
                };
                
                const response = await fetch(SCRIPT_URL, {
                    method: 'POST',
                    mode: 'no-cors',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(testData)
                });
                
                addResult('✅ POST request sent (no-cors mode - cannot read response)', 'success');
                addResult('💡 Check your Google Spreadsheet for new data', 'info');
                
            } catch (error) {
                addResult(`❌ POST Error: ${error.message}`, 'error');
            }
        }
        
        async function testFormSubmission() {
            addResult('📋 Testing form submission...', 'info');
            try {
                const formData = new FormData();
                formData.append('name', 'Form Test User');
                formData.append('email', '<EMAIL>');
                formData.append('message', 'Test message via FormData');
                
                const response = await fetch(SCRIPT_URL, {
                    method: 'POST',
                    mode: 'no-cors',
                    body: formData
                });
                
                addResult('✅ Form submission sent', 'success');
                addResult('💡 Check your Google Spreadsheet for new data', 'info');
                
            } catch (error) {
                addResult(`❌ Form Error: ${error.message}`, 'error');
            }
        }
        
        async function handleQuickTest(e) {
            e.preventDefault();
            
            const name = document.getElementById('testName').value;
            const email = document.getElementById('testEmail').value;
            const message = document.getElementById('testMessage').value;
            
            addResult('🚀 Submitting quick test form...', 'info');
            
            try {
                const formData = {
                    name: name,
                    email: email,
                    message: message,
                    timestamp: new Date().toISOString()
                };
                
                const response = await fetch(SCRIPT_URL, {
                    method: 'POST',
                    mode: 'no-cors',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(formData)
                });
                
                addResult('✅ Quick test submitted successfully!', 'success');
                addResult('📊 Check your Google Spreadsheet for the new row', 'info');
                
            } catch (error) {
                addResult(`❌ Quick test failed: ${error.message}`, 'error');
            }
        }
        
        function addResult(message, type) {
            const resultsDiv = document.getElementById('results');
            const alertClass = type === 'error' ? 'alert-danger' : 
                             type === 'success' ? 'alert-success' : 
                             type === 'warning' ? 'alert-warning' : 'alert-info';
            
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert ${alertClass} mb-2`;
            alertDiv.innerHTML = `<small>${new Date().toLocaleTimeString()}</small><br>${message}`;
            
            resultsDiv.appendChild(alertDiv);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }
        
        // Initial info
        document.addEventListener('DOMContentLoaded', function() {
            addResult('🔧 Debug tool loaded. Click buttons above to test your Google Apps Script.', 'info');
        });
    </script>
</body>
</html>
