<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google Sheets API Test</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h3>Google Sheets API Test</h3>
                    </div>
                    <div class="card-body">
                        <form id="testForm">
                            <div class="mb-3">
                                <label for="apiKey" class="form-label">API Key</label>
                                <input type="text" class="form-control" id="apiKey" placeholder="Enter your Google Sheets API key">
                            </div>
                            <div class="mb-3">
                                <label for="spreadsheetId" class="form-label">Spreadsheet ID</label>
                                <input type="text" class="form-control" id="spreadsheetId" placeholder="Enter your spreadsheet ID">
                            </div>
                            <div class="mb-3">
                                <label for="testName" class="form-label">Test Name</label>
                                <input type="text" class="form-control" id="testName" value="Test User">
                            </div>
                            <div class="mb-3">
                                <label for="testEmail" class="form-label">Test Email</label>
                                <input type="email" class="form-control" id="testEmail" value="<EMAIL>">
                            </div>
                            <button type="submit" class="btn btn-primary">Test API Connection</button>
                        </form>
                        
                        <div id="results" class="mt-4"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.getElementById('testForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const apiKey = document.getElementById('apiKey').value.trim();
            const spreadsheetId = document.getElementById('spreadsheetId').value.trim();
            const testName = document.getElementById('testName').value.trim();
            const testEmail = document.getElementById('testEmail').value.trim();
            
            if (!apiKey || !spreadsheetId) {
                showResult('Please enter both API key and Spreadsheet ID', 'error');
                return;
            }
            
            const submitBtn = this.querySelector('button[type="submit"]');
            submitBtn.textContent = 'Testing...';
            submitBtn.disabled = true;
            
            try {
                // Test 1: Check if we can read the spreadsheet
                showResult('Testing API connection...', 'info');
                
                const readUrl = `https://sheets.googleapis.com/v4/spreadsheets/${spreadsheetId}/values/Sheet1!A1:F1?key=${apiKey}`;
                const readResponse = await fetch(readUrl);
                
                if (!readResponse.ok) {
                    const errorData = await readResponse.json();
                    throw new Error(`Read test failed: ${errorData.error?.message || readResponse.statusText}`);
                }
                
                showResult('✅ Read access successful!', 'success');
                
                // Test 2: Try to append data
                showResult('Testing write access...', 'info');
                
                const values = [
                    [
                        new Date().toLocaleString(),
                        testName,
                        testEmail,
                        'API Test',
                        'This is a test message from the API test tool',
                        'Test'
                    ]
                ];
                
                const writeUrl = `https://sheets.googleapis.com/v4/spreadsheets/${spreadsheetId}/values/Sheet1!A:F:append?valueInputOption=USER_ENTERED&key=${apiKey}`;
                
                const writeResponse = await fetch(writeUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ values: values })
                });
                
                if (!writeResponse.ok) {
                    const errorData = await writeResponse.json();
                    throw new Error(`Write test failed: ${errorData.error?.message || writeResponse.statusText}`);
                }
                
                const writeResult = await writeResponse.json();
                showResult('✅ Write access successful!', 'success');
                showResult(`✅ Data added to range: ${writeResult.updates?.updatedRange}`, 'success');
                showResult('🎉 All tests passed! Your integration is ready to use.', 'success');
                
            } catch (error) {
                console.error('Test failed:', error);
                showResult(`❌ Test failed: ${error.message}`, 'error');
                
                // Provide helpful troubleshooting tips
                if (error.message.includes('API key')) {
                    showResult('💡 Tip: Check if your API key is correct and has access to Google Sheets API', 'warning');
                } else if (error.message.includes('not found')) {
                    showResult('💡 Tip: Check if your spreadsheet ID is correct and the spreadsheet exists', 'warning');
                } else if (error.message.includes('permission')) {
                    showResult('💡 Tip: Make sure your spreadsheet is shared publicly with "Editor" access', 'warning');
                }
                
            } finally {
                submitBtn.textContent = 'Test API Connection';
                submitBtn.disabled = false;
            }
        });
        
        function showResult(message, type) {
            const resultsDiv = document.getElementById('results');
            const alertClass = type === 'error' ? 'alert-danger' : 
                             type === 'success' ? 'alert-success' : 
                             type === 'warning' ? 'alert-warning' : 'alert-info';
            
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert ${alertClass}`;
            alertDiv.textContent = message;
            
            resultsDiv.appendChild(alertDiv);
            
            // Auto-scroll to bottom
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }
    </script>
</body>
</html>
