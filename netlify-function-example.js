// Netlify Function Example (save as netlify/functions/submit-form.js)
// This approach works well with static site hosting

const { GoogleSpreadsheet } = require('google-spreadsheet');

exports.handler = async (event, context) => {
    // Only allow POST requests
    if (event.httpMethod !== 'POST') {
        return {
            statusCode: 405,
            body: JSON.stringify({ error: 'Method not allowed' })
        };
    }

    try {
        // Parse the form data
        const data = JSON.parse(event.body);
        
        // Validate required fields
        if (!data.name || !data.email || !data.subject || !data.message) {
            return {
                statusCode: 400,
                body: JSON.stringify({ error: 'Missing required fields' })
            };
        }

        // Initialize Google Sheets document
        const doc = new GoogleSpreadsheet(process.env.GOOGLE_SHEET_ID);
        
        // Authenticate with service account
        await doc.useServiceAccountAuth({
            client_email: process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL,
            private_key: process.env.GOOGLE_PRIVATE_KEY.replace(/\\n/g, '\n'),
        });

        // Load document info
        await doc.loadInfo();
        
        // Get the first sheet
        const sheet = doc.sheetsByIndex[0];
        
        // Add the row
        await sheet.addRow({
            Timestamp: new Date().toISOString(),
            Name: data.name,
            Email: data.email,
            Subject: data.subject,
            Message: data.message,
            Status: 'New'
        });

        return {
            statusCode: 200,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type',
                'Access-Control-Allow-Methods': 'POST'
            },
            body: JSON.stringify({ 
                success: true, 
                message: 'Form submitted successfully' 
            })
        };

    } catch (error) {
        console.error('Error:', error);
        return {
            statusCode: 500,
            body: JSON.stringify({ 
                error: 'Internal server error',
                message: error.message 
            })
        };
    }
};

// Package.json dependencies needed:
/*
{
  "dependencies": {
    "google-spreadsheet": "^3.3.0"
  }
}
*/

// Environment variables needed in Netlify:
// GOOGLE_SHEET_ID=your_spreadsheet_id
// GOOGLE_SERVICE_ACCOUNT_EMAIL=your_service_account_email
// GOOGLE_PRIVATE_KEY=your_private_key
