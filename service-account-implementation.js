// Service Account Implementation (More Secure)
// This requires a backend server to keep credentials secure

// Backend implementation (Node.js/Express example)
const express = require('express');
const { GoogleSpreadsheet } = require('google-spreadsheet');
const cors = require('cors');

const app = express();
app.use(cors());
app.use(express.json());

// Service account credentials (keep these secure!)
const GOOGLE_SERVICE_ACCOUNT = {
    client_email: process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL,
    private_key: process.env.GOOGLE_PRIVATE_KEY.replace(/\\n/g, '\n'),
};

const SPREADSHEET_ID = process.env.GOOGLE_SPREADSHEET_ID;

app.post('/api/submit-form', async (req, res) => {
    try {
        const { name, email, subject, message } = req.body;
        
        // Validation
        if (!name || !email || !subject || !message) {
            return res.status(400).json({ 
                error: 'Missing required fields' 
            });
        }
        
        // Email validation
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            return res.status(400).json({ 
                error: 'Invalid email format' 
            });
        }
        
        // Initialize Google Sheets
        const doc = new GoogleSpreadsheet(SPREADSHEET_ID);
        await doc.useServiceAccountAuth(GOOGLE_SERVICE_ACCOUNT);
        await doc.loadInfo();
        
        const sheet = doc.sheetsByIndex[0];
        
        // Add row to spreadsheet
        await sheet.addRow({
            Timestamp: new Date().toLocaleString(),
            Name: name,
            Email: email,
            Subject: subject,
            Message: message,
            Status: 'New'
        });
        
        res.json({ 
            success: true, 
            message: 'Form submitted successfully' 
        });
        
    } catch (error) {
        console.error('Error:', error);
        res.status(500).json({ 
            error: 'Internal server error',
            message: error.message 
        });
    }
});

app.listen(3000, () => {
    console.log('Server running on port 3000');
});

// Frontend JavaScript to use with this backend
function setupServiceAccountIntegration() {
    document.querySelector('form').addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const formData = {
            name: document.getElementById('name').value.trim(),
            email: document.getElementById('email').value.trim(),
            subject: document.getElementById('subject').value,
            message: document.getElementById('message').value.trim()
        };
        
        // Validation
        if (!formData.name || !formData.email || !formData.subject || !formData.message) {
            showAlert('Please fill in all required fields.', 'error');
            return;
        }
        
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.textContent;
        submitBtn.textContent = 'Sending...';
        submitBtn.disabled = true;
        
        try {
            const response = await fetch('/api/submit-form', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData)
            });
            
            const result = await response.json();
            
            if (response.ok) {
                showAlert('Thank you for your message! We will get back to you soon.', 'success');
                this.reset();
            } else {
                showAlert(result.error || 'There was an error sending your message.', 'error');
            }
            
        } catch (error) {
            console.error('Error:', error);
            showAlert('There was an error sending your message. Please try again.', 'error');
        } finally {
            submitBtn.textContent = originalText;
            submitBtn.disabled = false;
        }
    });
}

// Package.json dependencies:
/*
{
  "dependencies": {
    "express": "^4.18.2",
    "google-spreadsheet": "^3.3.0",
    "cors": "^2.8.5"
  }
}
*/

// Environment variables needed:
// GOOGLE_SERVICE_ACCOUNT_EMAIL=<EMAIL>
// GOOGLE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----\n"
// GOOGLE_SPREADSHEET_ID=your_spreadsheet_id
