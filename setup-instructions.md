# Google Sheets API Integration Setup Instructions

## 🚀 Complete Setup Guide for Google Sheets API

### Step 1: Create Google Spreadsheet
1. Go to [sheets.google.com](https://sheets.google.com)
2. Create a new spreadsheet
3. Name it "Contact Form Submissions"
4. Copy the spreadsheet ID from the URL (the long string between `/d/` and `/edit`)
   - Example: `https://docs.google.com/spreadsheets/d/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms/edit`
   - ID: `1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms`

### Step 2: Enable Google Sheets API
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Go to "APIs & Services" > "Library"
4. Search for "Google Sheets API"
5. Click on it and press "Enable"

### Step 3: Create API Key
1. In Google Cloud Console, go to "APIs & Services" > "Credentials"
2. Click "Create Credentials" > "API Key"
3. Copy the API key (keep it secure!)
4. Click "Restrict Key" to add restrictions:
   - Application restrictions: HTTP referrers (web sites)
   - Add your website domain (e.g., `https://yourdomain.com/*`)
   - API restrictions: Select "Google Sheets API"
5. Save the restrictions

### Step 4: Make Spreadsheet Public (Option 1 - Easier)
1. Open your Google Spreadsheet
2. Click "Share" button (top right)
3. Click "Change to anyone with the link"
4. Set permission to "Editor"
5. Copy the link

### Step 4 Alternative: Use Service Account (Option 2 - More Secure)
1. In Google Cloud Console, go to "APIs & Services" > "Credentials"
2. Click "Create Credentials" > "Service Account"
3. Fill in the details and create
4. Download the JSON key file
5. Share your spreadsheet with the service account email (found in JSON)
6. Use service account authentication in your code

### Step 5: Update Your HTML File
1. Open your `index.html` file
2. Replace `YOUR_GOOGLE_SHEETS_API_KEY` with your actual API key
3. Replace `YOUR_SPREADSHEET_ID` with your actual spreadsheet ID
4. Save the file

### Step 6: Test the Integration
1. Open your HTML file in a browser
2. Fill out the contact form
3. Submit the form
4. Check your Google Spreadsheet for the new row

## 🔧 Configuration Details

### Required Replacements in HTML:
```javascript
const GOOGLE_SHEETS_CONFIG = {
    apiKey: 'AIzaSyC4YourActualAPIKeyHere', // Your API key
    spreadsheetId: '1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms', // Your spreadsheet ID
    range: 'Sheet1!A:F' // Range where data will be added
};
```

### Spreadsheet Structure:
The code will automatically create these columns:
- **Column A**: Timestamp
- **Column B**: Name
- **Column C**: Email
- **Column D**: Subject
- **Column E**: Message
- **Column F**: Status

## 🛡️ Security Best Practices

1. **API Key Restrictions**: Always restrict your API key to your domain
2. **Spreadsheet Permissions**: Use service account for production
3. **Input Validation**: The code includes client-side validation
4. **Error Handling**: Comprehensive error handling is implemented

## 🐛 Troubleshooting

### Common Issues:

**"API key not valid" error:**
- Check if Google Sheets API is enabled
- Verify API key restrictions match your domain
- Ensure API key has access to Google Sheets API

**"Spreadsheet not found" error:**
- Verify spreadsheet ID is correct
- Check if spreadsheet is shared publicly or with service account
- Ensure spreadsheet exists and is accessible

**"Permission denied" error:**
- Make spreadsheet public with "Editor" access, OR
- Use service account and share spreadsheet with service account email

**CORS errors:**
- This shouldn't happen with Google Sheets API
- If it does, check your domain restrictions

### Testing Steps:
1. Open browser developer tools (F12)
2. Go to Console tab
3. Submit the form
4. Check for any error messages
5. Verify data appears in your spreadsheet

## 📱 Production Considerations

1. **Rate Limiting**: Google Sheets API has quotas (100 requests per 100 seconds per user)
2. **Caching**: Consider caching for high-traffic sites
3. **Backup**: Regularly backup your spreadsheet data
4. **Monitoring**: Set up alerts for API quota usage

## 🔄 Next Steps

1. Set up email notifications for new submissions
2. Create a dashboard to view and manage submissions
3. Add data export functionality
4. Implement automated responses
