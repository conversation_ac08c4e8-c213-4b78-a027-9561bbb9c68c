# Google Sheets Integration Setup Instructions

## Step 1: Create Google Spreadsheet
1. Go to [sheets.google.com](https://sheets.google.com)
2. Create a new spreadsheet
3. Name it "Contact Form Submissions"
4. Copy the spreadsheet ID from the URL (the long string between `/d/` and `/edit`)
   - Example: `https://docs.google.com/spreadsheets/d/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms/edit`
   - ID: `1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms`

## Step 2: Create Google Apps Script
1. Go to [script.google.com](https://script.google.com)
2. Click "New Project"
3. Replace the default code with the code from `google-apps-script.js`
4. Replace `YOUR_SPREADSHEET_ID` with your actual spreadsheet ID
5. Save the project (Ctrl+S) and name it "Contact Form Handler"

## Step 3: Set Up Spreadsheet Headers
1. In the Apps Script editor, run the `setupSpreadsheet()` function once
2. This will create headers in your spreadsheet

## Step 4: Deploy as Web App
1. In Apps Script, click "Deploy" > "New Deployment"
2. Choose "Web app" as the type
3. Set execute as: "Me"
4. Set access: "Anyone" (or "Anyone with Google account" for more security)
5. Click "Deploy"
6. Copy the web app URL - you'll need this for your HTML form

## Step 5: Update HTML Form
Replace the form submission JavaScript in your HTML file with the code provided.

## Security Notes
- The web app URL will be public, but only accepts POST requests with specific data
- Consider adding additional validation in the Apps Script
- For production use, consider implementing rate limiting
- You can restrict access to "Anyone with Google account" for better security
