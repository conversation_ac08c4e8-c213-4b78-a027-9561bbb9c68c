// Google Apps Script code to save form data to Google Sheets
function doPost(e) {
  try {
    // Replace with your actual spreadsheet ID
    const SPREADSHEET_ID = '1F7iLrt7wQsftAEZOvIHhJCtAd54bJWWFJ7FKnEidrUE';
    const sheet = SpreadsheetApp.openById(SPREADSHEET_ID).getActiveSheet();

    // Parse the form data
    const data = JSON.parse(e.postData.contents);

    // Get current timestamp
    const timestamp = new Date();

    // Prepare the row data (matching your current structure without subject)
    const rowData = [
      timestamp,
      data.name,
      data.email,
      data.message,
      'New' // Status column
    ];

    // Add the data to the sheet
    sheet.appendRow(rowData);

    // Return success response
    return ContentService
      .createTextOutput(JSON.stringify({
        status: 'success',
        message: 'Data saved successfully'
      }))
      .setMimeType(ContentService.MimeType.JSON);

  } catch (error) {
    // Return error response
    return ContentService
      .createTextOutput(JSON.stringify({
        status: 'error',
        message: error.toString()
      }))
      .setMimeType(ContentService.MimeType.JSON);
  }
}

function doGet(e) {
  // Handle GET requests (for testing)
  return ContentService
    .createTextOutput('Contact Form API is working!')
    .setMimeType(ContentService.MimeType.TEXT);
}

// Function to set up the spreadsheet headers (run this once)
function setupSpreadsheet() {
  const SPREADSHEET_ID = '1F7iLrt7wQsftAEZOvIHhJCtAd54bJWWFJ7FKnEidrUE';
  const sheet = SpreadsheetApp.openById(SPREADSHEET_ID).getActiveSheet();

  // Set headers (without subject column)
  const headers = ['Timestamp', 'Name', 'Email', 'Message', 'Status'];
  sheet.getRange(1, 1, 1, headers.length).setValues([headers]);

  // Format the header row
  const headerRange = sheet.getRange(1, 1, 1, headers.length);
  headerRange.setFontWeight('bold');
  headerRange.setBackground('#4285f4');
  headerRange.setFontColor('white');

  // Auto-resize columns
  sheet.autoResizeColumns(1, headers.length);
}
