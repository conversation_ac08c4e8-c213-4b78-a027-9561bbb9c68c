// Google Apps Script code to save form data to Google Sheets
function doPost(e) {
  try {
    // Replace with your actual spreadsheet ID
    const SPREADSHEET_ID = '1F7iLrt7wQsftAEZOvIHhJCtAd54bJWWFJ7FKnEidrUE';
    const spreadsheet = SpreadsheetApp.openById(SPREADSHEET_ID);

    // Parse the form data
    const data = JSON.parse(e.postData.contents);

    // Get current timestamp
    const timestamp = new Date();

    // Check if this is an enquiry form or contact form
    if (data.formType === 'enquiry') {
      // Handle enquiry form - save to specific sheet (gid=6661519)
      const enquirySheet = spreadsheet.getSheetByName('Sheet2') || spreadsheet.getSheets()[1];

      const rowData = [
        timestamp,
        data.name,
        data.mobile,
        data.organization || '',
        data.callTime || '',
        data.enquiry,
        'New' // Status column
      ];

      enquirySheet.appendRow(rowData);

    } else {
      // Handle contact form - save to main sheet
      const contactSheet = spreadsheet.getSheets()[0]; // First sheet

      const rowData = [
        timestamp,
        data.name,
        data.email,
        data.message,
        'New' // Status column
      ];

      contactSheet.appendRow(rowData);
    }

    // Return success response
    return ContentService
      .createTextOutput(JSON.stringify({
        status: 'success',
        message: 'Data saved successfully'
      }))
      .setMimeType(ContentService.MimeType.JSON);

  } catch (error) {
    // Return error response
    return ContentService
      .createTextOutput(JSON.stringify({
        status: 'error',
        message: error.toString()
      }))
      .setMimeType(ContentService.MimeType.JSON);
  }
}

function doGet(e) {
  // Handle GET requests (for testing)
  return ContentService
    .createTextOutput('Contact Form API is working!')
    .setMimeType(ContentService.MimeType.TEXT);
}

// Function to set up the spreadsheet headers (run this once)
function setupSpreadsheet() {
  const SPREADSHEET_ID = '1F7iLrt7wQsftAEZOvIHhJCtAd54bJWWFJ7FKnEidrUE';
  const spreadsheet = SpreadsheetApp.openById(SPREADSHEET_ID);

  // Setup contact form sheet (first sheet)
  const contactSheet = spreadsheet.getSheets()[0];
  const contactHeaders = ['Timestamp', 'Name', 'Email', 'Message', 'Status'];
  contactSheet.getRange(1, 1, 1, contactHeaders.length).setValues([contactHeaders]);

  // Format the contact sheet header row
  const contactHeaderRange = contactSheet.getRange(1, 1, 1, contactHeaders.length);
  contactHeaderRange.setFontWeight('bold');
  contactHeaderRange.setBackground('#4285f4');
  contactHeaderRange.setFontColor('white');
  contactSheet.autoResizeColumns(1, contactHeaders.length);

  // Setup enquiry form sheet (second sheet or create new one)
  let enquirySheet = spreadsheet.getSheetByName('Sheet2');
  if (!enquirySheet) {
    enquirySheet = spreadsheet.insertSheet('Sheet2');
  }

  const enquiryHeaders = ['Timestamp', 'Name', 'Mobile', 'Organization', 'Preferred Call Time', 'Enquiry', 'Status'];
  enquirySheet.getRange(1, 1, 1, enquiryHeaders.length).setValues([enquiryHeaders]);

  // Format the enquiry sheet header row
  const enquiryHeaderRange = enquirySheet.getRange(1, 1, 1, enquiryHeaders.length);
  enquiryHeaderRange.setFontWeight('bold');
  enquiryHeaderRange.setBackground('#8967B3');
  enquiryHeaderRange.setFontColor('white');
  enquirySheet.autoResizeColumns(1, enquiryHeaders.length);
}
