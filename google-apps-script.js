// Google Apps Script code to save form data to Google Sheets
function doPost(e) {
  try {
    // Spreadsheet IDs
    const CONTACT_SPREADSHEET_ID = '1F7iLrt7wQsftAEZOvIHhJCtAd54bJWWFJ7FKnEidrUE'; // Original contact form sheet
    const ENQUIRY_SPREADSHEET_ID = '1GjgtphYHV9ZdKLdcXf5ILau6wjM307z5di5Vt0e80Tk'; // New enquiry form sheet

    // Parse the form data
    const data = JSON.parse(e.postData.contents);

    // Get current timestamp
    const timestamp = new Date();

    // Check if this is an enquiry form or contact form
    if (data.formType === 'enquiry') {
      // Handle enquiry form - save to new spreadsheet
      const enquirySpreadsheet = SpreadsheetApp.openById(ENQUIRY_SPREADSHEET_ID);
      const enquirySheet = enquirySpreadsheet.getSheets()[0]; // First sheet of enquiry spreadsheet

      const rowData = [
        timestamp,
        data.name,
        data.mobile,
        data.organization || '',
        data.callTime || '',
        data.enquiry,
        'New' // Status column
      ];

      enquirySheet.appendRow(rowData);

    } else {
      // Handle contact form - save to original spreadsheet
      const contactSpreadsheet = SpreadsheetApp.openById(CONTACT_SPREADSHEET_ID);
      const contactSheet = contactSpreadsheet.getSheets()[0]; // First sheet of contact spreadsheet

      const rowData = [
        timestamp,
        data.name,
        data.email,
        data.message,
        'New' // Status column
      ];

      contactSheet.appendRow(rowData);
    }

    // Return success response
    return ContentService
      .createTextOutput(JSON.stringify({
        status: 'success',
        message: 'Data saved successfully'
      }))
      .setMimeType(ContentService.MimeType.JSON);

  } catch (error) {
    // Return error response
    return ContentService
      .createTextOutput(JSON.stringify({
        status: 'error',
        message: error.toString()
      }))
      .setMimeType(ContentService.MimeType.JSON);
  }
}

function doGet(e) {
  // Handle GET requests (for testing)
  return ContentService
    .createTextOutput('Contact Form API is working!')
    .setMimeType(ContentService.MimeType.TEXT);
}

// Function to set up the spreadsheet headers (run this once)
function setupSpreadsheet() {
  // Spreadsheet IDs
  const CONTACT_SPREADSHEET_ID = '1F7iLrt7wQsftAEZOvIHhJCtAd54bJWWFJ7FKnEidrUE';
  const ENQUIRY_SPREADSHEET_ID = '1GjgtphYHV9ZdKLdcXf5ILau6wjM307z5di5Vt0e80Tk';

  // Setup contact form spreadsheet
  const contactSpreadsheet = SpreadsheetApp.openById(CONTACT_SPREADSHEET_ID);
  const contactSheet = contactSpreadsheet.getSheets()[0];
  const contactHeaders = ['Timestamp', 'Name', 'Email', 'Message', 'Status'];
  contactSheet.getRange(1, 1, 1, contactHeaders.length).setValues([contactHeaders]);

  // Format the contact sheet header row
  const contactHeaderRange = contactSheet.getRange(1, 1, 1, contactHeaders.length);
  contactHeaderRange.setFontWeight('bold');
  contactHeaderRange.setBackground('#4285f4');
  contactHeaderRange.setFontColor('white');
  contactSheet.autoResizeColumns(1, contactHeaders.length);

  // Setup enquiry form spreadsheet
  const enquirySpreadsheet = SpreadsheetApp.openById(ENQUIRY_SPREADSHEET_ID);
  const enquirySheet = enquirySpreadsheet.getSheets()[0];
  const enquiryHeaders = ['Timestamp', 'Name', 'Mobile', 'Organization', 'Preferred Call Time', 'Enquiry', 'Status'];
  enquirySheet.getRange(1, 1, 1, enquiryHeaders.length).setValues([enquiryHeaders]);

  // Format the enquiry sheet header row
  const enquiryHeaderRange = enquirySheet.getRange(1, 1, 1, enquiryHeaders.length);
  enquiryHeaderRange.setFontWeight('bold');
  enquiryHeaderRange.setBackground('#8967B3');
  enquiryHeaderRange.setFontColor('white');
  enquirySheet.autoResizeColumns(1, enquiryHeaders.length);
}
