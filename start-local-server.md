# Local Development Server Setup

## Option 1: Python (if you have Python installed)

### Python 3:
```bash
cd /Users/<USER>/Desktop/ed
python3 -m http.server 8000
```

### Python 2:
```bash
cd /Users/<USER>/Desktop/ed
python -m SimpleHTTPServer 8000
```

Then open: http://localhost:8000

## Option 2: Node.js (if you have Node.js installed)

### Install a simple server:
```bash
npm install -g http-server
```

### Run the server:
```bash
cd /Users/<USER>/Desktop/ed
http-server -p 8000
```

Then open: http://localhost:8000

## Option 3: PHP (if you have PHP installed)

```bash
cd /Users/<USER>/Desktop/ed
php -S localhost:8000
```

Then open: http://localhost:8000

## Option 4: Live Server Extension (VS Code)

If you're using VS Code:
1. Install "Live Server" extension
2. Right-click on index.html
3. Select "Open with Live Server"

## Option 5: Using Browser Extensions

Some browsers have extensions that can serve local files:
- "Web Server for Chrome" extension
- "Live Server" for various browsers

## Why Use a Local Server?

1. **Proper HTTP protocol**: APIs work better with http:// than file://
2. **CORS handling**: Avoids cross-origin issues
3. **Real testing environment**: Mimics how your site will work when deployed
4. **API key restrictions**: You can set restrictions to localhost:8000

## Setting API Key Restrictions for Development

Once you have a local server running:

1. Go to Google Cloud Console
2. Edit your API key
3. Set "HTTP referrers" restriction
4. Add: `http://localhost:8000/*`
5. Also add your production domain when ready

This keeps your API key secure while allowing local development.
