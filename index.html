<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css">
    <title>Teknosolve</title>
    <style>
        html {
            scroll-behavior: smooth;
        }

        .navbar {
            transition: all 0.3s ease;
        }

        .navbar.scrolled {
            background-color: rgba(248, 249, 250, 0.95) !important;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        section {
            scroll-margin-top: 70px;
        }

        .btn {
            transition: all 0.3s ease;
        }

        .card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-light bg-light">
        <div class="container-fluid">
            <img src="https://www.pngfind.com/pngs/m/597-5975111_unknown-small-copy-small-circle-png-transparent-png.png" alt="" height='20px'/>
            <a class="navbar-brand" href="#">Teknosolve</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link active" aria-current="page" href="#home">Home</a>
                    </li>
                    <li>
                        <a class="nav-link" href="#courses">Courses</a>
                    </li>
                    <li>
                        <a class="nav-link" href="#payments">Payments</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#about">About us</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#contact">Contact</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <header id="home" class="text-white text-center py-5" style="background-color: #8967B3;">
        <div class="container">
            <h1>Welcome to Teknosolve</h1>
            <p class="lead">Training that delivers</p>
            <a href="#about" class="btn btn-light btn-lg me-3">Learn More</a>
            <a href="#projects" class="btn btn-outline-light btn-lg">View Projects</a>
        </div>
    </header>

    <!-- About Section -->
    <section id="about" class="py-5 bg-light">
        <div class="container">
            <div class="row">
                <div class="col-lg-12 text-center mb-5">
                    <h2 class="display-4">About Teknosolve</h2>
                    <p class="lead text-muted">
                        At Teknosolve, we believe that the right training can change everything. 
                    </p>
                    <p>
                        We are a leading company dedicated to empowering students and corporate professional with the skill and strategies they need to achieve their goals. 
                    </p>
                    <p>
                        Wheather it's cracking competetive exams, cleaving interview rounds in MNCs, or enhancing team performance, Teknosolve deliver impactful, practical and customized training solutions.
                    </p>
                    <p>
                        With s team of eperienced trainers, updated course content and a focus on results, we have helped so many individuals and organisations, bridge the gap between potential and performance. 
                    </p>
                    <p>
                        Our mission: To equip learners with the knowledge, confidence and tools to succeed in academics, careers and life. 
                    </p>
                </div>
            </div>
        </div>
    </section>

   

    <section class="py-5">
        <div class="container">
            <h2 class="text-center mb-4">Courses</h2>
            <div class="row">
                <div class="col-md-4">
                    <div class="card mb-4 h-100">
                        <div class="card-header bg-primary text-white">
                            <h5 class="card-title mb-0">Full Stack Web Development</h5>
                        </div>
                        <div class="card-body">
                            <p class="card-text">Master modern web development with HTML5, CSS3, JavaScript, React, Node.js, and database management. Build real-world applications from scratch.</p>
                            <ul class="list-unstyled">
                                <li><i class="text-success">✓</i> 40+ hours of content</li>
                                <li><i class="text-success">✓</i> 15 hands-on projects</li>
                                <li><i class="text-success">✓</i> Certificate included</li>
                            </ul>
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="badge bg-success">Beginner</span>
                                <strong class="text-primary">$199</strong>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card mb-4 h-100">
                        <div class="card-header bg-info text-white">
                            <h5 class="card-title mb-0">Data Science & Analytics</h5>
                        </div>
                        <div class="card-body">
                            <p class="card-text">Learn Python, pandas, NumPy, machine learning algorithms, and data visualization. Work with real datasets and build predictive models.</p>
                            <ul class="list-unstyled">
                                <li><i class="text-success">✓</i> 35+ hours of content</li>
                                <li><i class="text-success">✓</i> 12 practical projects</li>
                                <li><i class="text-success">✓</i> Industry datasets</li>
                            </ul>
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="badge bg-warning">Intermediate</span>
                                <strong class="text-primary">$249</strong>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card mb-4 h-100">
                        <div class="card-header bg-success text-white">
                            <h5 class="card-title mb-0">Mobile App Development</h5>
                        </div>
                        <div class="card-body">
                            <p class="card-text">Create native iOS and Android apps using React Native and Flutter. Learn app store deployment and monetization strategies.</p>
                            <ul class="list-unstyled">
                                <li><i class="text-success">✓</i> 30+ hours of content</li>
                                <li><i class="text-success">✓</i> 8 complete apps</li>
                                <li><i class="text-success">✓</i> App store guidance</li>
                            </ul>
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="badge bg-success">Beginner</span>
                                <strong class="text-primary">$179</strong>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card mb-4 h-100">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="card-title mb-0">Cloud Computing & DevOps</h5>
                        </div>
                        <div class="card-body">
                            <p class="card-text">Master AWS, Docker, Kubernetes, CI/CD pipelines, and infrastructure as code. Learn to deploy and scale applications in the cloud.</p>
                            <ul class="list-unstyled">
                                <li><i class="text-success">✓</i> 45+ hours of content</li>
                                <li><i class="text-success">✓</i> 20 lab exercises</li>
                                <li><i class="text-success">✓</i> AWS certification prep</li>
                            </ul>
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="badge bg-danger">Advanced</span>
                                <strong class="text-primary">$299</strong>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card mb-4 h-100">
                        <div class="card-header bg-secondary text-white">
                            <h5 class="card-title mb-0">Cybersecurity Fundamentals</h5>
                        </div>
                        <div class="card-body">
                            <p class="card-text">Learn ethical hacking, network security, cryptography, and risk assessment. Hands-on labs with real security tools and scenarios.</p>
                            <ul class="list-unstyled">
                                <li><i class="text-success">✓</i> 38+ hours of content</li>
                                <li><i class="text-success">✓</i> 25 security labs</li>
                                <li><i class="text-success">✓</i> Industry tools access</li>
                            </ul>
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="badge bg-warning">Intermediate</span>
                                <strong class="text-primary">$229</strong>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card mb-4 h-100">
                        <div class="card-header bg-dark text-white">
                            <h5 class="card-title mb-0">AI & Machine Learning</h5>
                        </div>
                        <div class="card-body">
                            <p class="card-text">Dive deep into artificial intelligence, neural networks, deep learning, and natural language processing. Build intelligent applications.</p>
                            <ul class="list-unstyled">
                                <li><i class="text-success">✓</i> 50+ hours of content</li>
                                <li><i class="text-success">✓</i> 18 AI projects</li>
                                <li><i class="text-success">✓</i> GPU cloud access</li>
                            </ul>
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="badge bg-danger">Advanced</span>
                                <strong class="text-primary">$349</strong>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="py-5">
        <div class="container">
            <h2 class="text-center mb-4">Testimonials</h2>
            <div id="testimonialCarousel" class="carousel slide" data-bs-ride="carousel">
                <div class="carousel-inner">
                    <div class="carousel-item active">
                        <div class="d-flex justify-content-center">
                            <div class="card border-0 shadow-sm mb-5" style="max-width: 600px;">
                                <div class="card-body text-center p-4">
                                    <div class="mb-3">
                                        <img src="https://via.placeholder.com/80x80/007bff/ffffff?text=JD" class="rounded-circle" alt="John Doe" width="80" height="80">
                                    </div>
                                    <h5 class="card-title">John Doe</h5>
                                    <p class="text-muted small">Senior Software Engineer at TechCorp</p>
                                    <p class="card-text fst-italic">"The Full Stack Web Development course completely transformed my career. The hands-on projects and real-world applications gave me the confidence to transition from a junior to senior role. The instructor's expertise and support throughout the journey was exceptional."</p>
                                    <div class="text-warning">
                                        ★★★★★
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="carousel-item">
                        <div class="d-flex justify-content-center">
                            <div class="card border-0 shadow-sm mb-5" style="max-width: 600px;">
                                <div class="card-body text-center p-4">
                                    <div class="mb-3">
                                        <img src="https://via.placeholder.com/80x80/28a745/ffffff?text=JS" class="rounded-circle" alt="Jane Smith" width="80" height="80">
                                    </div>
                                    <h5 class="card-title">Jane Smith</h5>
                                    <p class="text-muted small">Data Scientist at Analytics Pro</p>
                                    <p class="card-text fst-italic">"The Data Science & Analytics course exceeded my expectations. Working with real datasets and building predictive models gave me practical skills I use daily. The course structure is perfect for working professionals like myself."</p>
                                    <div class="text-warning">
                                        ★★★★★
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="carousel-item">
                        <div class="d-flex justify-content-center">
                            <div class="card border-0 shadow-sm mb-5" style="max-width: 600px;">
                                <div class="card-body text-center p-4">
                                    <div class="mb-3">
                                        <img src="https://via.placeholder.com/80x80/dc3545/ffffff?text=SW" class="rounded-circle" alt="Sam Wilson" width="80" height="80">
                                    </div>
                                    <h5 class="card-title">Sam Wilson</h5>
                                    <p class="text-muted small">Mobile App Developer & Entrepreneur</p>
                                    <p class="card-text fst-italic">"Thanks to the Mobile App Development course, I launched my own app that now has over 50k downloads! The deployment strategies and monetization insights were invaluable for my startup journey."</p>
                                    <div class="text-warning">
                                        ★★★★★
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <button class="carousel-control-prev" type="button" data-bs-target="#testimonialCarousel" data-bs-slide="prev">
                    <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                    <span class="visually-hidden">Previous</span>
                </button>
                <button class="carousel-control-next" type="button" data-bs-target="#testimonialCarousel" data-bs-slide="next">
                    <span class="carousel-control-next-icon" aria-hidden="true"></span>
                    <span class="visually-hidden">Next</span>
                </button>
                <div class="carousel-indicators">
                    <button type="button" data-bs-target="#testimonialCarousel" data-bs-slide-to="0" class="active" aria-current="true" aria-label="Slide 1"></button>
                    <button type="button" data-bs-target="#testimonialCarousel" data-bs-slide-to="1" aria-label="Slide 2"></button>
                    <button type="button" data-bs-target="#testimonialCarousel" data-bs-slide-to="2" aria-label="Slide 3"></button>
                </div>
            </div>
        </div>
    </section>

   

    <section id="contact" class="py-5 bg-light">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-8">
                    <h2 class="text-center mb-4">Contact Us</h2>
                    <p class="text-center text-muted mb-5">Ready to start your technology journey? Get in touch with us today!</p>
                    <div class="row">
                        <div class="col-md-8">
                            <form>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="name" class="form-label">Full Name</label>
                                        <input type="text" class="form-control" id="name" placeholder="Your Full Name" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="email" class="form-label">Email Address</label>
                                        <input type="email" class="form-control" id="email" placeholder="<EMAIL>" required>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="message" class="form-label">Message</label>
                                    <textarea class="form-control" id="message" rows="5" placeholder="Tell us about your project or inquiry..." required></textarea>
                                </div>
                                <button type="submit" class="btn btn-primary btn-lg">Send Message</button>
                            </form>
                        </div>
                        <div class="col-md-4">
                            <div class="card border-0 shadow-sm">
                                <div class="card-body">
                                    <h5 class="card-title">Get In Touch</h5>
                                    <div class="mb-3">
                                        <strong>📧 Email:</strong><br>
                                        <a href="mailto:<EMAIL>" class="text-decoration-none"><EMAIL></a>
                                    </div>
                                    <div class="mb-3">
                                        <strong>📞 Phone:</strong><br>
                                        <a href="tel:7338020032" class="text-decoration-none">7338020032</a>
                                    </div>
                                    <div class="mb-3">
                                        <strong>📍 Address:</strong><br>
                                        Teknosolve limited</br>
                                        1st Cross, Amrita nagar phase 2
                                        Kasavanahalli, Off Sarjapur road
                                        Bengaluru 560035
                                    </div>
                                    <div class="mb-3">
                                        <strong>🕒 Business Hours:</strong><br>
                                        Mon - Fri: 9:00 AM - 4:00 PM<br>
                                    </div>
                                    
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <footer class="bg-light text-center py-4">
        <div class="container">
            <p class="mb-0">&copy; 2025 Teknosolve. All rights reserved.</p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Update active navigation link based on scroll position
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar');
            const navLinks = document.querySelectorAll('.nav-link');
            const sections = document.querySelectorAll('section[id], header[id]');

            // Add scrolled class to navbar
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }

            // Update active nav link
            let current = '';
            sections.forEach(section => {
                const sectionTop = section.offsetTop - 100;
                const sectionHeight = section.offsetHeight;
                if (window.scrollY >= sectionTop && window.scrollY < sectionTop + sectionHeight) {
                    current = section.getAttribute('id');
                }
            });

            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === '#' + current) {
                    link.classList.add('active');
                }
            });
        });

        // Google Sheets API Configuration
        const GOOGLE_SHEETS_CONFIG = {
            apiKey: 'AIzaSyDufj3l_6Nh4SeTqtEMgVhCxz3G58tVcNs', // Replace with your API key
            spreadsheetId: '1F7iLrt7wQsftAEZOvIHhJCtAd54bJWWFJ7FKnEidrUE', // Replace with your spreadsheet ID
            range: 'Sheet1!A:F' // The range where data will be appended
        };

        // Google Sheets API Integration Class
        class GoogleSheetsAPI {
            constructor(config) {
                this.apiKey = config.apiKey;
                this.spreadsheetId = config.spreadsheetId;
                this.range = config.range;
                this.baseUrl = 'https://sheets.googleapis.com/v4/spreadsheets';
            }

            async appendData(formData) {
                const values = [
                    [
                        new Date().toLocaleString(), // Timestamp
                        formData.name,
                        formData.email,
                        formData.message,
                        'New' // Status
                    ]
                ];

                const url = `${this.baseUrl}/${this.spreadsheetId}/values/${this.range}:append`;

                const response = await fetch(`${url}?valueInputOption=USER_ENTERED&key=${this.apiKey}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        values: values
                    })
                });

                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(`Google Sheets API Error: ${errorData.error?.message || response.statusText}`);
                }

                return await response.json();
            }

            async initializeSheet() {
                // Check if sheet exists and has headers
                try {
                    const url = `${this.baseUrl}/${this.spreadsheetId}/values/Sheet1!A1:F1`;
                    const response = await fetch(`${url}?key=${this.apiKey}`);

                    if (response.ok) {
                        const data = await response.json();
                        if (!data.values || data.values.length === 0) {
                            // Add headers if they don't exist
                            await this.addHeaders();
                        }
                    }
                } catch (error) {
                    console.warn('Could not check/initialize headers:', error);
                }
            }

            async addHeaders() {
                const headers = [['Timestamp', 'Name', 'Email', 'Message', 'Status']];
                const url = `${this.baseUrl}/${this.spreadsheetId}/values/Sheet1!A1:F1`;

                await fetch(`${url}?valueInputOption=USER_ENTERED&key=${this.apiKey}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        values: headers
                    })
                });
            }
        }

        // Initialize Google Sheets API
        const sheetsAPI = new GoogleSheetsAPI(GOOGLE_SHEETS_CONFIG);

        // Form submission handler with Google Sheets API integration
        document.querySelector('form').addEventListener('submit', async function(e) {
            e.preventDefault();

            // Get form data
            const name = document.getElementById('name').value.trim();
            const email = document.getElementById('email').value.trim();
            const message = document.getElementById('message').value.trim();

            // Simple validation
            if (!name || !email ||  !message) {
                showAlert('Please fill in all required fields.', 'error');
                return;
            }

            // Email validation
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                showAlert('Please enter a valid email address.', 'error');
                return;
            }

            // Show loading state
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;
            submitBtn.textContent = 'Sending...';
            submitBtn.disabled = true;

            // Prepare data for Google Sheets
            const formData = {
                name: name,
                email: email,
                message: message
            };

            try {
                // Send data to Google Sheets using API
                await sheetsAPI.appendData(formData);

                // Success
                showAlert('Thank you for your message! We will get back to you soon.', 'success');
                this.reset();

            } catch (error) {
                console.error('Error saving to Google Sheets:', error);

                // Show specific error message
                let errorMessage = 'There was an error sending your message. Please try again.';
                if (error.message.includes('API key')) {
                    errorMessage = 'Configuration error. Please contact the administrator.';
                } else if (error.message.includes('not found')) {
                    errorMessage = 'Spreadsheet not found. Please contact the administrator.';
                } else if (error.message.includes('permission')) {
                    errorMessage = 'Permission denied. Please contact the administrator.';
                }

                showAlert(errorMessage, 'error');
            } finally {
                // Reset button state
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
            }
        });

        // Initialize sheet headers when page loads
        document.addEventListener('DOMContentLoaded', async function() {
            try {
                await sheetsAPI.initializeSheet();
            } catch (error) {
                console.warn('Could not initialize sheet:', error);
            }
        });

        // Function to show alerts
        function showAlert(message, type) {
            // Remove existing alerts
            const existingAlert = document.querySelector('.custom-alert');
            if (existingAlert) {
                existingAlert.remove();
            }

            // Create alert element
            const alert = document.createElement('div');
            alert.className = `alert alert-${type === 'error' ? 'danger' : 'success'} alert-dismissible fade show custom-alert`;
            alert.style.position = 'fixed';
            alert.style.top = '20px';
            alert.style.right = '20px';
            alert.style.zIndex = '9999';
            alert.style.minWidth = '300px';

            alert.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(alert);

            // Auto remove after 5 seconds
            setTimeout(() => {
                if (alert && alert.parentNode) {
                    alert.remove();
                }
            }, 5000);
        }
    </script>
</body>
</html>