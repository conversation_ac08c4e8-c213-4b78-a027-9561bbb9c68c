<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <title>Teknosolve</title>
    <style>
        html {
            scroll-behavior: smooth;
        }

        .navbar {
            transition: all 0.3s ease;
        }

        .navbar.scrolled {
            background-color: rgba(248, 249, 250, 0.95) !important;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        section {
            scroll-margin-top: 70px;
        }

        .btn {
            transition: all 0.3s ease;
        }

        .card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-light bg-light">
        <div class="container-fluid">
            <img src="./assets/logo.png" alt="" height='60px'/>
            <!-- <a class="navbar-brand" href="#">Teknosolve</a> -->
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link active" aria-current="page" href="#home">Home</a>
                    </li>
                    <li>
                        <a class="nav-link" href="#courses">Courses</a>
                    </li>
                    <li>
                        <a class="nav-link" href="#payments">Payments</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#about">About us</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#contact">Contact</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <header id="home" class="text-dark text-center py-5" style="background-color: white;">
        <div class="container">
            <h1 class="text-primary">Welcome to Teknosolve</h1>
            <p class="lead text-muted">Training that delivers</p>
            <div class="row mt-5 justify-content-center">
                <div class="col-md-5">
                    <div class="card text-dark h-100 shadow-lg" style=" border: none; border-radius: 15px;">
                        <div class="card-body d-flex flex-column">
                            <h5 class="card-title mb-4">🎯 What We Offer</h5>
                            <ul class="list-unstyled flex-grow-1">
                                <li class="mb-2">✅ Industry expert trainer</li>
                                <li class="mb-2">✅ Customizable program</li>
                                <li class="mb-2">✅ Online/offline Training</li>
                                <li class="mb-2">✅ Assessment</li>
                                <li class="mb-2">✅ Certification</li>
                                <li class="mb-2">✅ Placement Assistance</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <!-- <div class="col-md-5">
                    <div class="card text-white h-100 shadow-lg" style="background-color: #B7B1F2; border: none; border-radius: 15px;">
                        <div class="card-body d-flex flex-column">
                            <h5 class="card-title mb-4">📊 Our Impact</h5>
                            <div class="row text-center flex-grow-1 align-items-center">
                                <div class="col-6">
                                    <div class="mb-3">
                                        <h2 class="display-4 fw-bold">5000+</h2>
                                        <p class="mb-0">Successful Stories</p>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="mb-3">
                                        <h2 class="display-4 fw-bold">20+</h2>
                                        <p class="mb-0">Happy Clients</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div> -->
            </div>
            <div class="mt-4">
                <a href="#contact" class="btn btn-lg px-5 py-3 fw-bold text-white shadow-lg" style="background-color: #B7B1F2; border: none; border-radius: 25px;">Request a Demo</a>
            </div>
            
        </div>
    </header>

    <!-- About Section -->
    <section id="about" class="py-5 bg-light">
        <div class="container">
            <div class="row">
                <div class="col-lg-12 text-center mb-5">
                    <h2 class="text-center mb-4">About Teknosolve</h2>
                    <p class="lead text-muted">
                        At Teknosolve, we believe that the right training can change everything. 
                    </p>
                    <p>
                        We are a leading company dedicated to empowering students and corporate professional with the skill and strategies they need to achieve their goals. 
                    </p>
                    <p>
                        Wheather it's cracking competetive exams, cleaving interview rounds in MNCs, or enhancing team performance, Teknosolve deliver impactful, practical and customized training solutions.
                    </p>
                    <p>
                        With s team of eperienced trainers, updated course content and a focus on results, we have helped so many individuals and organisations, bridge the gap between potential and performance. 
                    </p>
                    <p>
                        Our mission: To equip learners with the knowledge, confidence and tools to succeed in academics, careers and life. 
                    </p>
                </div>
            </div>
        </div>
    </section>

   

    <section class="py-5" id="courses">
        <div class="container">
            <h2 class="text-center mb-4">Courses</h2>
            <div class="row">
                <div class="col-md-4">
                    <div class="card mb-4 h-100">
                        <div class="card-header bg-primary text-white">
                            <h5 class="card-title mb-0">Pleacement Training</h5>
                        </div>
                        <div class="card-body">
                            <p class="card-text"></p>   
                            <ul class="list-unstyled">
                                <li><i class="text-success">✓</i> Quntitative Aptitude</li>
                                <li><i class="text-success">✓</i> Logical resoning</li>
                                <li><i class="text-success">✓</i> Verbal Ability</li>
                                <li><i class="text-success">✓</i> Soft Skills</li>
                                <li><i class="text-success">✓</i> Mock Interviews</li>
                            </ul>
                            <!-- <div class="d-flex justify-content-between align-items-center">
                                <span class="badge bg-success">Beginner</span>
                                <strong class="text-primary">$199</strong>
                            </div> -->
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card mb-4 h-100">
                        <div class="card-header bg-info text-white">
                            <h5 class="card-title mb-0">Competetive Exams, Training</h5>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled">
                                <li><i class="text-success">✓</i> CAT, MAT, GMAT</li>
                                <li><i class="text-success">✓</i> Bank PO</li>
                                <li><i class="text-success">✓</i> CLAT, IPMAT and other entrance test</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card mb-4 h-100">
                        <div class="card-header bg-success text-white">
                            <h5 class="card-title mb-0">Corptate Training</h5>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled">
                                <li><i class="text-success">✓</i> Communication Skills</li>
                                <li><i class="text-success">✓</i> Leadership Skills</li>
                                <li><i class="text-success">✓</i> Problem Solving Skills</li>
                                <li><i class="text-success">✓</i> Time Management</li>
                                <li><i class="text-success">✓</i> Buiness Etiquette</li>
                                <li><i class="text-success">✓</i> Emotional Intelligence</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card mb-4 h-100">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="card-title mb-0">Subject Training</h5>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled">
                                <li><i class="text-success">✓</i> Mathematics and Science</li>
                                <li><i class="text-success">✓</i> 7th to 10th</li>
                                <li><i class="text-success">✓</i> ICSE/CBSE/State Board</li>
                            </ul>
                            
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card mb-4 h-100">
                        <div class="card-header bg-secondary text-white">
                            <h5 class="card-title mb-0">Mock Interview</h5>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled">
                                <li><i class="text-success">✓</i> Expert techical/HR interviews from top MNCs</li>
                                <li><i class="text-success">✓</i> Individual training on speaking skills and body language</li>
                            </ul>
                            
                        </div>
                    </div>
                </div>
               
            </div>
        </div>
    </section>

    <!-- <section class="py-5">
        <div class="container">
            <h2 class="text-center mb-4">Testimonials</h2>
            <div id="testimonialCarousel" class="carousel slide" data-bs-ride="carousel">
                <div class="carousel-inner">
                    <div class="carousel-item active">
                        <div class="d-flex justify-content-center">
                            <div class="card border-0 shadow-sm mb-5" style="max-width: 600px;">
                                <div class="card-body text-center p-4">
                                    <div class="mb-3">
                                        <img src="https://via.placeholder.com/80x80/007bff/ffffff?text=JD" class="rounded-circle" alt="John Doe" width="80" height="80">
                                    </div>
                                    <h5 class="card-title">John Doe</h5>
                                    <p class="text-muted small">Senior Software Engineer at TechCorp</p>
                                    <p class="card-text fst-italic">"The Full Stack Web Development course completely transformed my career. The hands-on projects and real-world applications gave me the confidence to transition from a junior to senior role. The instructor's expertise and support throughout the journey was exceptional."</p>
                                    <div class="text-warning">
                                        ★★★★★
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="carousel-item">
                        <div class="d-flex justify-content-center">
                            <div class="card border-0 shadow-sm mb-5" style="max-width: 600px;">
                                <div class="card-body text-center p-4">
                                    <div class="mb-3">
                                        <img src="https://via.placeholder.com/80x80/28a745/ffffff?text=JS" class="rounded-circle" alt="Jane Smith" width="80" height="80">
                                    </div>
                                    <h5 class="card-title">Jane Smith</h5>
                                    <p class="text-muted small">Data Scientist at Analytics Pro</p>
                                    <p class="card-text fst-italic">"The Data Science & Analytics course exceeded my expectations. Working with real datasets and building predictive models gave me practical skills I use daily. The course structure is perfect for working professionals like myself."</p>
                                    <div class="text-warning">
                                        ★★★★★
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="carousel-item">
                        <div class="d-flex justify-content-center">
                            <div class="card border-0 shadow-sm mb-5" style="max-width: 600px;">
                                <div class="card-body text-center p-4">
                                    <div class="mb-3">
                                        <img src="https://via.placeholder.com/80x80/dc3545/ffffff?text=SW" class="rounded-circle" alt="Sam Wilson" width="80" height="80">
                                    </div>
                                    <h5 class="card-title">Sam Wilson</h5>
                                    <p class="text-muted small">Mobile App Developer & Entrepreneur</p>
                                    <p class="card-text fst-italic">"Thanks to the Mobile App Development course, I launched my own app that now has over 50k downloads! The deployment strategies and monetization insights were invaluable for my startup journey."</p>
                                    <div class="text-warning">
                                        ★★★★★
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <button class="carousel-control-prev" type="button" data-bs-target="#testimonialCarousel" data-bs-slide="prev">
                    <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                    <span class="visually-hidden">Previous</span>
                </button>
                <button class="carousel-control-next" type="button" data-bs-target="#testimonialCarousel" data-bs-slide="next">
                    <span class="carousel-control-next-icon" aria-hidden="true"></span>
                    <span class="visually-hidden">Next</span>
                </button>
                <div class="carousel-indicators">
                    <button type="button" data-bs-target="#testimonialCarousel" data-bs-slide-to="0" class="active" aria-current="true" aria-label="Slide 1"></button>
                    <button type="button" data-bs-target="#testimonialCarousel" data-bs-slide-to="1" aria-label="Slide 2"></button>
                    <button type="button" data-bs-target="#testimonialCarousel" data-bs-slide-to="2" aria-label="Slide 3"></button>
                </div>
            </div>
        </div>
    </section> -->
 <!-- Payment Section -->
 <section id="payments" class="py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-lg-12 text-center mb-5">
                <h2 class="text-center mb-4">Payment Options</h2>
            </div>
        </div>

        <div class="row justify-content-center">

            <!-- Bank Account Details -->
            <div class="row mt-5 justify-content-center">
                <div class="col-lg-8">
                    <div class="card border-0 shadow-lg" style="background: linear-gradient(135deg, #8967B3 0%, #6a4c93 100%);">
                        <div class="card-body text-white p-5">
                            <div class="text-center mb-4">
                                <i class="fas fa-university fa-3x mb-3"></i>
                                <h4 class="card-title">Bank Account Details</h4>
                                <p class="mb-0">For direct bank transfers and payments</p>
                            </div>

                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <div class="text-center p-3 bg-white bg-opacity-10 rounded">
                                        <h6 class="mb-2">Account Name</h6>
                                        <p class="mb-0 fw-bold">TEKNOSOLVE</p>
                                    </div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <div class="text-center p-3 bg-white bg-opacity-10 rounded">
                                        <h6 class="mb-2">Account Number</h6>
                                        <p class="mb-0 fw-bold">***********</p>
                                    </div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <div class="text-center p-3 bg-white bg-opacity-10 rounded">
                                        <h6 class="mb-2">Bank Name</h6>
                                        <p class="mb-0 fw-bold">STATE BANK OF INDIA</p>
                                    </div>
                                </div>
                            </div>

                            <div class="row mt-3">
                                <div class="col-md-4 mb-3">
                                    <div class="text-center p-3 bg-white bg-opacity-10 rounded">
                                        <h6 class="mb-2">Branch</h6>
                                        <p class="mb-0 fw-bold">KASAVANAHALLI, Bengaluru</p>
                                    </div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <div class="text-center p-3 bg-white bg-opacity-10 rounded">
                                        <h6 class="mb-2">IFSC Code</h6>
                                        <p class="mb-0 fw-bold">SBIN0016212</p>
                                    </div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <div class="text-center p-3 bg-white bg-opacity-10 rounded">
                                        <h6 class="mb-2">MICR Code</h6>
                                        <p class="mb-0 fw-bold">560002198</p>
                                    </div>
                                </div>
                            </div>

                            <div class="text-center mt-4">
                                <div class="alert alert-light d-inline-block">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <strong>Note:</strong> Please share payment confirmation details at
                                    <a href="mailto:<EMAIL>" class="text-decoration-none fw-bold"><EMAIL></a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
   

    <section id="contact" class="py-5">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-8">
                    <h2 class="text-center mb-4">Contact Us</h2>
                    <p class="text-center text-muted mb-5">Ready to start your technology journey? Get in touch with us today!</p>
                    <div class="row">
                        <div class="col-md-8">
                            <form>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="name" class="form-label">Full Name</label>
                                        <input type="text" class="form-control" id="name" placeholder="Your Full Name" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="email" class="form-label">Email Address</label>
                                        <input type="email" class="form-control" id="email" placeholder="<EMAIL>" required>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="message" class="form-label">Message</label>
                                    <textarea class="form-control" id="message" rows="5" placeholder="Tell us about your project or inquiry..." required></textarea>
                                </div>
                                <button type="submit" class="btn btn-primary btn-lg">Send Message</button>
                            </form>
                        </div>
                        <div class="col-md-4">
                            <div class="card border-0 shadow-sm">
                                <div class="card-body">
                                    <h5 class="card-title">Get In Touch</h5>
                                    <div class="mb-3">
                                        <strong>📧 Email:</strong><br>
                                        <a href="mailto:<EMAIL>" class="text-decoration-none"><EMAIL></a>
                                    </div>
                                    <div class="mb-3">
                                        <strong>📞 Phone:</strong><br>
                                        <a href="tel:7338020032" class="text-decoration-none">7338020032</a>
                                    </div>
                                    <div class="mb-3">
                                        <strong>📍 Address:</strong><br>
                                        Teknosolve limited</br>
                                        1st Cross, Amrita nagar phase 2
                                        Kasavanahalli, Off Sarjapur road
                                        Bengaluru 560035
                                    </div>
                                    <div class="mb-3">
                                        <strong>🕒 Business Hours:</strong><br>
                                        Mon - Fri: 9:00 AM - 4:00 PM<br>
                                    </div>
                                    
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

   

    <footer class="bg-light text-center py-4">
        <div class="container">
            <p class="mb-0">&copy; 2025 Teknosolve. All rights reserved.</p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Update active navigation link based on scroll position
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar');
            const navLinks = document.querySelectorAll('.nav-link');
            const sections = document.querySelectorAll('section[id], header[id]');

            // Add scrolled class to navbar
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }

            // Update active nav link
            let current = '';
            sections.forEach(section => {
                const sectionTop = section.offsetTop - 100;
                const sectionHeight = section.offsetHeight;
                if (window.scrollY >= sectionTop && window.scrollY < sectionTop + sectionHeight) {
                    current = section.getAttribute('id');
                }
            });

            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === '#' + current) {
                    link.classList.add('active');
                }
            });
        });

        // Google Apps Script Configuration
        // Replace this URL with your Google Apps Script web app URL
        const GOOGLE_APPS_SCRIPT_URL = "https://script.google.com/macros/s/AKfycbxOREj967v9pCoasZcmXqkcwytCO4XOMo1CcGamV26UGRm-OC_oycFfenTiDJ3rRhVV/exec";

        // Form submission handler with Google Apps Script integration
        document.querySelector('form').addEventListener('submit', async function(e) {
            e.preventDefault();

            // Get form data
            const name = document.getElementById('name').value.trim();
            const email = document.getElementById('email').value.trim();
            const message = document.getElementById('message').value.trim();

            // Simple validation
            if (!name || !email || !message) {
                showAlert('Please fill in all required fields.', 'error');
                return;
            }

            // Email validation
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                showAlert('Please enter a valid email address.', 'error');
                return;
            }

            // Show loading state
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;
            submitBtn.textContent = 'Sending...';
            submitBtn.disabled = true;

            // Prepare data for Google Sheets
            const formData = {
                name: name,
                email: email,
                message: message,
                timestamp: new Date().toISOString()
            };

            try {
                // Send data to Google Apps Script
                const response = await fetch(GOOGLE_APPS_SCRIPT_URL, {
                    method: 'POST',
                    mode: 'no-cors', // Required for Google Apps Script
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(formData)
                });

                // Note: With no-cors mode, we can't read the response
                // But if no error is thrown, it likely succeeded
                showAlert('Thank you for your message! We will get back to you soon.', 'success');
                this.reset();

            } catch (error) {
                console.error('Error saving to Google Sheets:', error);
                showAlert('There was an error sending your message. Please try again.', 'error');
            } finally {
                // Reset button state
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
            }
        });

        // Function to show alerts
        function showAlert(message, type) {
            // Remove existing alerts
            const existingAlert = document.querySelector('.custom-alert');
            if (existingAlert) {
                existingAlert.remove();
            }

            // Create alert element
            const alert = document.createElement('div');
            alert.className = `alert alert-${type === 'error' ? 'danger' : 'success'} alert-dismissible fade show custom-alert`;
            alert.style.position = 'fixed';
            alert.style.top = '20px';
            alert.style.right = '20px';
            alert.style.zIndex = '9999';
            alert.style.minWidth = '300px';

            alert.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(alert);

            // Auto remove after 5 seconds
            setTimeout(() => {
                if (alert && alert.parentNode) {
                    alert.remove();
                }
            }, 5000);
        }
    </script>
</body>
</html>