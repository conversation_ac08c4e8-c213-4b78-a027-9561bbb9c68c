// Alternative Method: Direct Google Sheets API Integration
// This requires API key and is more complex but gives more control

class GoogleSheetsIntegration {
    constructor(apiKey, spreadsheetId, range = 'Sheet1!A:F') {
        this.apiKey = apiKey;
        this.spreadsheetId = spreadsheetId;
        this.range = range;
        this.baseUrl = 'https://sheets.googleapis.com/v4/spreadsheets';
    }

    async appendData(formData) {
        const values = [
            [
                new Date().toISOString(),
                formData.name,
                formData.email,
                formData.subject,
                formData.message,
                'New'
            ]
        ];

        const url = `${this.baseUrl}/${this.spreadsheetId}/values/${this.range}:append`;
        
        const response = await fetch(`${url}?valueInputOption=RAW&key=${this.apiKey}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                values: values
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return await response.json();
    }
}

// Usage in your form submission
function setupGoogleSheetsAPI() {
    // Replace with your actual API key and spreadsheet ID
    const API_KEY = 'YOUR_GOOGLE_SHEETS_API_KEY';
    const SPREADSHEET_ID = 'YOUR_SPREADSHEET_ID';
    
    const sheetsIntegration = new GoogleSheetsIntegration(API_KEY, SPREADSHEET_ID);
    
    document.querySelector('form').addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const formData = {
            name: document.getElementById('name').value.trim(),
            email: document.getElementById('email').value.trim(),
            subject: document.getElementById('subject').value,
            message: document.getElementById('message').value.trim()
        };
        
        // Validation
        if (!formData.name || !formData.email || !formData.subject || !formData.message) {
            alert('Please fill in all required fields.');
            return;
        }
        
        try {
            const submitBtn = this.querySelector('button[type="submit"]');
            submitBtn.textContent = 'Sending...';
            submitBtn.disabled = true;
            
            await sheetsIntegration.appendData(formData);
            
            alert('Thank you for your message! We will get back to you soon.');
            this.reset();
            
        } catch (error) {
            console.error('Error saving to Google Sheets:', error);
            alert('There was an error sending your message. Please try again.');
        } finally {
            const submitBtn = this.querySelector('button[type="submit"]');
            submitBtn.textContent = 'Send Message';
            submitBtn.disabled = false;
        }
    });
}

// Call this function when the page loads
// setupGoogleSheetsAPI();
