<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google Sheets API Debug Tool</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card">
                    <div class="card-header">
                        <h3>🔍 Google Sheets API Debug Tool</h3>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <strong>Your Current Configuration:</strong><br>
                            API Key: AIzaSyDufj3l_6Nh4SeTqtEMgVhCxz3G58tVcNs<br>
                            Spreadsheet ID: 1F7iLrt7wQsftAEZOvIHhJCtAd54bJWWFJ7FKnEidrUE
                        </div>
                        
                        <button id="testBtn" class="btn btn-primary mb-3">🧪 Run Diagnostic Tests</button>
                        <button id="fixPermissionsBtn" class="btn btn-warning mb-3 ms-2">🔧 Try to Fix Permissions</button>
                        
                        <div id="results" class="mt-4"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const API_KEY = 'AIzaSyDufj3l_6Nh4SeTqtEMgVhCxz3G58tVcNs';
        const SPREADSHEET_ID = '1F7iLrt7wQsftAEZOvIHhJCtAd54bJWWFJ7FKnEidrUE';
        
        document.getElementById('testBtn').addEventListener('click', runDiagnostics);
        document.getElementById('fixPermissionsBtn').addEventListener('click', tryFixPermissions);
        
        async function runDiagnostics() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '';
            
            addResult('🚀 Starting diagnostic tests...', 'info');
            
            // Test 1: Check if API key is valid
            addResult('Test 1: Checking API key validity...', 'info');
            try {
                const testUrl = `https://sheets.googleapis.com/v4/spreadsheets/${SPREADSHEET_ID}?key=${API_KEY}`;
                const response = await fetch(testUrl);
                const data = await response.json();
                
                if (response.ok) {
                    addResult('✅ API key is valid and can access the spreadsheet', 'success');
                    addResult(`📊 Spreadsheet title: "${data.properties.title}"`, 'success');
                } else {
                    addResult(`❌ API Error: ${data.error.message}`, 'error');
                    
                    if (data.error.message.includes('API key')) {
                        addResult('💡 Solution: Check if Google Sheets API is enabled in Google Cloud Console', 'warning');
                    } else if (data.error.message.includes('not found')) {
                        addResult('💡 Solution: Verify your spreadsheet ID is correct', 'warning');
                    } else if (data.error.message.includes('permission')) {
                        addResult('💡 Solution: Make your spreadsheet public or share it properly', 'warning');
                    }
                    return;
                }
            } catch (error) {
                addResult(`❌ Network Error: ${error.message}`, 'error');
                if (error.message.includes('CORS')) {
                    addResult('💡 Solution: You need to run this from a web server, not file://', 'warning');
                }
                return;
            }
            
            // Test 2: Check spreadsheet permissions
            addResult('Test 2: Checking read permissions...', 'info');
            try {
                const readUrl = `https://sheets.googleapis.com/v4/spreadsheets/${SPREADSHEET_ID}/values/Sheet1!A1:F1?key=${API_KEY}`;
                const readResponse = await fetch(readUrl);
                const readData = await readResponse.json();
                
                if (readResponse.ok) {
                    addResult('✅ Can read from spreadsheet', 'success');
                    if (readData.values && readData.values.length > 0) {
                        addResult(`📋 Found headers: ${readData.values[0].join(', ')}`, 'success');
                    } else {
                        addResult('📋 No headers found - will create them', 'info');
                    }
                } else {
                    addResult(`❌ Read Error: ${readData.error.message}`, 'error');
                }
            } catch (error) {
                addResult(`❌ Read Test Failed: ${error.message}`, 'error');
            }
            
            // Test 3: Check write permissions
            addResult('Test 3: Checking write permissions...', 'info');
            try {
                const testData = [['Test', 'API', '<EMAIL>', 'Debug test message', 'Test']];
                const writeUrl = `https://sheets.googleapis.com/v4/spreadsheets/${SPREADSHEET_ID}/values/Sheet1!A:E:append?valueInputOption=USER_ENTERED&key=${API_KEY}`;
                
                const writeResponse = await fetch(writeUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ values: testData })
                });
                
                const writeData = await writeResponse.json();
                
                if (writeResponse.ok) {
                    addResult('✅ Can write to spreadsheet', 'success');
                    addResult(`📝 Test data added to: ${writeData.updates.updatedRange}`, 'success');
                    addResult('🎉 All tests passed! Your integration should work.', 'success');
                } else {
                    addResult(`❌ Write Error: ${writeData.error.message}`, 'error');
                    if (writeData.error.message.includes('permission')) {
                        addResult('💡 Solution: Make sure spreadsheet has "Editor" permissions for "Anyone with the link"', 'warning');
                    }
                }
            } catch (error) {
                addResult(`❌ Write Test Failed: ${error.message}`, 'error');
            }
        }
        
        async function tryFixPermissions() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '';
            
            addResult('🔧 Attempting to fix common issues...', 'info');
            
            // Try to add headers if they don't exist
            try {
                const headers = [['Timestamp', 'Name', 'Email', 'Message', 'Status']];
                const url = `https://sheets.googleapis.com/v4/spreadsheets/${SPREADSHEET_ID}/values/Sheet1!A1:E1?valueInputOption=USER_ENTERED&key=${API_KEY}`;
                
                const response = await fetch(url, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ values: headers })
                });
                
                if (response.ok) {
                    addResult('✅ Headers added successfully', 'success');
                } else {
                    const data = await response.json();
                    addResult(`❌ Could not add headers: ${data.error.message}`, 'error');
                }
            } catch (error) {
                addResult(`❌ Header fix failed: ${error.message}`, 'error');
            }
        }
        
        function addResult(message, type) {
            const resultsDiv = document.getElementById('results');
            const alertClass = type === 'error' ? 'alert-danger' : 
                             type === 'success' ? 'alert-success' : 
                             type === 'warning' ? 'alert-warning' : 'alert-info';
            
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert ${alertClass} mb-2`;
            alertDiv.innerHTML = message;
            
            resultsDiv.appendChild(alertDiv);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }
        
        // Show current environment info
        document.addEventListener('DOMContentLoaded', function() {
            const resultsDiv = document.getElementById('results');
            addResult(`🌐 Current URL: ${window.location.href}`, 'info');
            addResult(`🔗 Protocol: ${window.location.protocol}`, 'info');
            
            if (window.location.protocol === 'file:') {
                addResult('⚠️ You are running from file:// protocol. This may cause CORS issues.', 'warning');
                addResult('💡 Recommendation: Use a local web server (python -m http.server 8000)', 'warning');
            }
        });
    </script>
</body>
</html>
